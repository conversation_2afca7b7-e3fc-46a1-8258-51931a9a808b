"use client";

import { CommonService } from "@/services/common/common.service";
import { useQuery } from "react-query";

export function useCountries() {
  const { data, isLoading } = useQuery(
    ["countries"],
    CommonService.getCountries
  );

  return {
    countries: data,
    isLoading,
  };
}

export function useCountryCities(code) {
  const { data, isLoading } = useQuery(
    ["countries", code, "cities"],
    () => CommonService.getCountryCities(code),
    {
      enabled: !!code,
    }
  );

  return {
    cities: data,
    isLoading,
  };
}

export function useStructuredCategories() {
  const { data, isLoading } = useQuery(
    ["structured-categories"],
    CommonService.getStructuredCategories
  );

  return {
    structuredCategories: data,
    isLoading,
  };
}

export function useFundingProduct(params) {
  const { data, isLoading } = useQuery(
    ["funding", "product", params],
    () => CommonService.getFundingProduct(params),
    {
      enabled: params?.category !== null && params?.power !== null,
    }
  );

  return {
    product: data,
    isLoading,
  };
}

export function useFundingSettings(options = {}) {
  const { data, isLoading } = useQuery(
    ["funding", "settings"],
    CommonService.getFundingSettings,
    {
      staleTime: Infinity,
      ...options,
    }
  );

  return {
    fundingSettings: data,
    isLoading,
  };
}

export function useGetPumpingSimulatorSolution(params) {
  const { data, isLoading } = useQuery(
    ["simulators", "pumping", "solution", params],
    () => CommonService.getPumpingSimulatorSolution(params)
  );

  return {
    data,
    isLoading,
  };
}

export function useGetInjectionSimulatorSolution(params) {
  const { data, isLoading } = useQuery(
    ["simulators", "injection", "solution", params],
    () => CommonService.getInjectionSimulatorSolution(params)
  );

  return {
    data,
    isLoading,
  };
}

export function useIp() {
  const { data } = useQuery(["ip"], CommonService.getIp);

  return {
    data,
  };
}
