import { CommonService } from "@/services/common";
import { MailIcon, PhoneIcon } from "lucide-react";
import Link from "next/link";
import NewsletterForm from "./newsletter-form";

export default async function Footer() {
  const settings = await CommonService.getSiteSettings();
  const featuredCategories = await CommonService.getFeaturedCategories();

  return (
    <footer className="py-10 bg-brand-primary">
      <div className="container-lg">
        <div className="grid grid-cols-3 lg:grid-cols-5 gap-x-4 gap-y-14 py-4">
          <div className="space-y-4 col-span-3 lg:col-span-2">
            {settings && settings?.store_white && (
              <div>
                <img
                  src={settings?.store_white}
                  className="lazyload h-10"
                  alt={settings?.store_name ?? ""}
                />
              </div>
            )}
            <div className="max-w-sm space-y-4">
              <p className="text-white">
                ECOWATT MAROC est une entreprise pionnière dans la fourniture de
                solutions énergétiques durables, offrant des services
                spécialisés en énergie solaire.
              </p>

              <div>
                <NewsletterForm />
                <span className="text-white leading-5 mt-2">
                  Restez en contact pour recevoir nos actualités.
                </span>
              </div>
              <div>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2 text-white">
                    <MailIcon size={20} /> {settings?.store_email}
                  </li>
                  <li className="flex items-center gap-2 text-white">
                    <PhoneIcon size={20} /> {settings?.store_phone}
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <h6 className="text-white font-black text-xl">Liens utiles</h6>
            <ul className="flex flex-col gap-2 font-medium text-lg [&>li>a]:text-white">
              <li>
                <Link href="/customer-guide">Guide d'utilisation</Link>
              </li>
              <li>
                <Link href="/order-guide">Comment commander</Link>
              </li>
              <li>
                <Link href="/track-order">Suivi de votre commande</Link>
              </li>
              <li>
                <Link href="/delivery">Modes de livraison</Link>
              </li>
              <li>
                <Link href="/payment">Modes de paiement</Link>
              </li>
              <li>
                <Link href="/became-seller">Devenir revendeur</Link>
              </li>
              <li>
                <Link href="/cgv">Conditions générales de vente (CGV)</Link>
              </li>
              <li>
                <Link href="/help-center">Centre d'aide</Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h6 className="text-white font-black text-xl">Explorer</h6>
            <ul className="flex flex-col gap-2 font-medium text-lg [&>li>a]:text-white">
              <li>
                <Link href="/about">À propos de nous</Link>
              </li>
              <li>
                <Link href="/simulators/calculation-notes">Simulateurs</Link>
              </li>
              <li>
                <Link href="/financement/wafasalaf">Financement</Link>
              </li>
              <li>
                <Link href="/gallery">Galerie</Link>
              </li>
              <li>
                <Link href="/academy">Académie</Link>
              </li>
              <li>
                <Link href="/blogs">Blogs</Link>
              </li>
              <li>
                <Link href="/careers">Carrières</Link>
              </li>
              <li>
                <Link href="/contact">Nos agences</Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h6 className="text-white font-black text-xl">Nos catégories</h6>
            <ul className="flex flex-col gap-2 font-medium text-lg [&>li>a]:text-white">
              {featuredCategories?.slice(0, 8)?.map((category) => (
                <li key={category.slug}>
                  <Link href={`/products?categories=${category?.slug}`}>
                    {category.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="flex items-center justify-between border-t border-dashed py-3">
          {settings && settings?.copyright && (
            <div>
              <h6 className="text-white">{settings?.copyright}</h6>
            </div>
          )}

          {settings && settings?.payment_images && (
            <div>
              <img
                src={settings?.payment_images}
                className="lazyload h-7"
                alt=""
              />
            </div>
          )}

          <div>
            {/* <h6 className="text-content">Stay connected :</h6> */}
            <ul className="flex items-center gap-2">
              {settings && settings?.sm_facebook && (
                <li>
                  <a
                    href={settings?.sm_facebook}
                    target="_blank"
                    rel="noreferrer"
                    className="w-8 aspect-square bg-[#3b5998] rounded-full grid place-items-center text-white"
                  >
                    <i className="fa-brands fa-facebook-f"></i>
                  </a>
                </li>
              )}
              {settings && settings?.sm_instagram && (
                <li>
                  <a
                    href={settings?.sm_instagram}
                    target="_blank"
                    rel="noreferrer"
                    className="w-8 aspect-square bg-[#bd32a2] rounded-full grid place-items-center text-white"
                  >
                    <i className="fa-brands fa-instagram"></i>
                  </a>
                </li>
              )}
              {settings && settings?.sm_linkedin && (
                <li>
                  <a
                    href={settings?.sm_linkedin}
                    target="_blank"
                    rel="noreferrer"
                    className="w-8 aspect-square bg-[#0070ac] rounded-full grid place-items-center text-white"
                  >
                    <i className="fa-brands fa-linkedin"></i>
                  </a>
                </li>
              )}
              {settings && settings?.sm_whatsapp && (
                <li>
                  <a
                    href={settings?.sm_whatsapp}
                    target="_blank"
                    rel="noreferrer"
                    className="w-8 aspect-square bg-[#25D366] rounded-full grid place-items-center text-white"
                  >
                    <i className="fa-brands fa-whatsapp"></i>
                  </a>
                </li>
              )}
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
}
