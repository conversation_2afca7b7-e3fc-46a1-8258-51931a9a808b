"use client";

import ProductPrice from "@/components/product-price";
import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { getProductStatus } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { useAuthContext } from "@/providers/auth-provider";
import { motion } from "framer-motion";
import { HeartIcon, MinusIcon, PlusIcon, ShoppingBagIcon } from "lucide-react";
import Link from "next/link";
import { useContext, useEffect, useState } from "react";
import { TailSpin } from "react-loader-spinner";

export default function ProductBox({ product }) {
  const productStatus = getProductStatus(product.is_active);

  const {
    wishListDataKeys,
    addToWishListMutation,
    removeFromWishListMutation,
    wishlistItemsLoading,
    addWishlistLoading,
    removeWishlistLoading,
    getWishlistItemsGuestLoading,
    storeGuestWishlistItem,
    removeGuestWishlistItem,
    cartDataChecker,
    addToCartMutation,
    storeGuestCartItem,
    showPopup,
    openPopup,
  } = useContext(CartAndWishlistProvider);

  const { authenticated } = useAuthContext();
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [addLoadingWishlist, setAddLoadingWishlist] = useState(false);
  const [removeLoadingWishlist, setRemoveLoadingWishlist] = useState(false);

  const [addItemInCart, setAddItemInCart] = useState(false);
  const [addLoadingCart, setAddLoadingCart] = useState(false);
  // const [removeLoadingCart, setRemoveLoadingCart] = useState(false)

  const [currentQuantity, setCurrentQuantity] = useState(0);
  const [quantity, setQuantity] = useState(0);

  useEffect(() => {
    if (wishListDataKeys.includes(product?.id)) {
      setIsWishlisted(true);
    } else {
      setIsWishlisted(false);
    }
  }, [wishListDataKeys]);

  useEffect(() => {
    let cartFilter = cartDataChecker.filter(function (el) {
      return el.id === product?.id;
    });
    if (product && cartFilter.length > 0) {
      cartFilter = cartFilter[0];
      setQuantity(cartFilter?.quantity);
      setCurrentQuantity(cartFilter?.quantity);
    } else {
      setAddItemInCart(false);
    }
  }, [product, cartDataChecker]);

  useEffect(() => {
    let timer = setTimeout(() => {
      if (
        setAddLoadingWishlist &&
        !getWishlistItemsGuestLoading &&
        !wishlistItemsLoading &&
        !addWishlistLoading &&
        !removeWishlistLoading
      ) {
        setAddLoadingWishlist(false);
      }
      if (
        removeLoadingWishlist &&
        !getWishlistItemsGuestLoading &&
        !wishlistItemsLoading &&
        !addWishlistLoading &&
        !removeWishlistLoading
      ) {
        setRemoveLoadingWishlist(false);
      }
    }, 0);
    return () => clearTimeout(timer);
  }, [
    addLoadingWishlist,
    removeLoadingWishlist,
    getWishlistItemsGuestLoading,
    wishlistItemsLoading,
    addWishlistLoading,
    removeWishlistLoading,
  ]);

  const handleAddToWishList = async () => {
    setAddLoadingWishlist(true);
    try {
      if (authenticated) await addToWishListMutation(product.id);
      else await storeGuestWishlistItem(product.id);
    } catch (error) {
      setAddLoadingWishlist(false);
    }
  };

  const handleRemoveFromWishList = async () => {
    setRemoveLoadingWishlist(true);
    try {
      if (authenticated) await removeFromWishListMutation(product.id);
      else await removeGuestWishlistItem(product.id);
    } catch (error) {
      setRemoveLoadingWishlist(false);
    }
  };

  const updateQuantity = async (qty, force = false) => {
    if (!isActive) return;

    const newQty = force ? qty : quantity + qty;

    if (newQty >= 0) {
      setQuantity(newQty);
    }
  };

  const handleAddToCard = async () => {
    if (isNaN(quantity) || quantity < 1 || quantity === currentQuantity) return;

    setAddLoadingCart(true);
    try {
      if (authenticated)
        await addToCartMutation({ id: product?.id, quantity: quantity });
      else await storeGuestCartItem({ id: product?.id, quantity: quantity });
      setCurrentQuantity(quantity);

      setAddLoadingCart(false);
      openPopup();
    } catch (error) {
      setAddLoadingCart(false);
    }
  };

  const isActive = [1, 2].includes(product?.is_active);

  return (
    <div
      className="relative border-2 rounded-xl h-full flex flex-col overflow-hidden"
      style={{
        borderColor: productStatus.color,
      }}
    >
      <div className="relative overflow-hidden">
        <Link
          href={`/products/${product.slug}`}
          className="block aspect-square bg-secondary/60"
        >
          <img
            src={product.full_image}
            alt={product.title}
            className="rounded-t"
          />
        </Link>

        {/* Status badge */}
        <div className="absolute top-1.5 left-1.5">
          <span
            className="px-2 py-0.5 rounded-md font-medium text-xs text-white"
            style={{ backgroundColor: productStatus.color }}
          >
            {productStatus.label}
          </span>
        </div>

        {/* Actions */}
        <div className="absolute top-2 right-2 flex flex-col gap-1">
          <motion.button
            className="!bg-white p-1 rounded-full"
            onClick={
              isWishlisted ? handleRemoveFromWishList : handleAddToWishList
            }
          >
            <motion.div
              initial={{ scale: 1 }}
              animate={{
                scale: isWishlisted ? [1, 1.2, 1] : 1,
                transition: { duration: 0.3 },
              }}
            >
              {addLoadingWishlist || removeLoadingWishlist ? (
                <TailSpin color="#2A3466" height={16} width={16} />
              ) : (
                <HeartIcon
                  size={16}
                  strokeWidth={2}
                  className={`stroke-brand-primary transition-colors duration-300 ${
                    isWishlisted
                      ? "text-red-500 stroke-red-500 fill-red-500"
                      : ""
                  }`}
                />
              )}
            </motion.div>
          </motion.button>
        </div>

        {product.active_offer && (
          <div className="absolute bottom-0 right-1">
            <span
              className="glow-effect px-2 py-0.5 rounded-md font-medium text-[0.65rem] text-white"
              style={{ backgroundColor: product.active_offer.offer.color }}
            >
              {product.active_offer.offer.title}
            </span>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="px-2.5 py-1.5 gap-2 flex-1 flex flex-col">
        <div>
          <Link href={`/products/${product.slug}`}>
            <h4 className="text-sm font-black line-clamp-2">{product.title}</h4>
          </Link>
        </div>
        <div className="mt-auto space-y-2">
          <ProductPrice product={product} />
          <div
            className={cn(
              "!bg-secondary p-1 flex items-center justify-between rounded",
              !isActive && "opacity-50 pointer-events-none"
            )}
          >
            <button
              className="!bg-gray-100 p-2 rounded"
              onClick={() => updateQuantity(-1)}
            >
              <MinusIcon size={12} strokeWidth={2} />
            </button>
            <input
              type="number"
              className=" text-center !bg-transparent !min-w-0"
              value={quantity}
              onChange={(e) => {
                const qty = parseInt(e.target.value) || 0;
                if (!isNaN(qty)) {
                  updateQuantity(qty, true);
                }
              }}
            />
            <button
              className="!bg-gray-100 p-2 rounded"
              onClick={() => updateQuantity(1)}
            >
              <PlusIcon size={12} strokeWidth={2} />
            </button>
          </div>
          <div
            className={cn(
              "flex justify-center",
              !isActive && "opacity-50 pointer-events-none"
            )}
          >
            <button
              className="w-full flex items-center gap-2 justify-center"
              onClick={handleAddToCard}
              disabled={!isActive && addLoadingCart}
            >
              {addLoadingCart ? (
                <TailSpin color="#2A3466" height={18} width={18} />
              ) : (
                <ShoppingBagIcon size={18} />
              )}
              <span className="font-medium !text-brand-primary">Ajouter</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
