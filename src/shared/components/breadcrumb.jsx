import SectionTitle from "@/components/section-title";
import Link from "next/link";
import { Home } from "react-feather";

export default function Breadcrumb({ title, items }) {
  return (
    <section className="breadscrumb-section pt-0">
      <div className="container-lg">
        <div className="row">
          <div className="col-12">
            <div className="breadscrumb-contain">
              <SectionTitle title={title} className="mb-0" />
              <nav>
                <ol className="breadcrumb mb-0">
                  <li className="breadcrumb-item">
                    <Link href={`/`}>
                      <Home />
                    </Link>
                  </li>
                  {items?.map((item, key) => (
                    <li
                      key={key}
                      className="breadcrumb-item"
                      aria-current="page"
                    >
                      {item.href ? (
                        <Link href={item.href}>{item.title}</Link>
                      ) : (
                        item.title
                      )}
                    </li>
                  ))}
                  <li className="breadcrumb-item" aria-current="page">
                    {title}
                  </li>
                </ol>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
