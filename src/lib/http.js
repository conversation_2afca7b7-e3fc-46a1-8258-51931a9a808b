import { isServer } from "./helpers";

class Http {
  constructor() {
    this.baseUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}/api`;
    this.defaultHeaders = {
      Accept: "application/json",
    };
  }

  async request(endpoint, options = {}) {
    let url = `${this.baseUrl}${endpoint}`;
    const headers = { ...this.defaultHeaders, ...options.headers };

    // prepare request cookies & auth token
    if (isServer()) {
      const { cookies } = require("next/headers");
      headers["Cookie"] = cookies().toString();
    }

    // prepare request body
    if (!(options.body instanceof FormData)) {
      options.body = JSON.stringify(options.body);
      headers["Content-Type"] = "application/json";
    }

    // prepare search params
    if (options.params) {
      const searchParams = new URLSearchParams();

      Object.entries(options.params).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach((item) => {
            searchParams.append(`${key}[]`, item);
          });
        } else {
          searchParams.append(key, value);
        }
      });

      url = `${url}?${searchParams.toString()}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: "include",
    });

    if (!response.ok) {
      const errorData = await response.json();

      throw {
        statusCode: response.status,
        ...errorData,
      };
    }

    return response.json();
  }

  get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: "GET" });
  }

  post(endpoint, body, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: "POST",
      body,
    });
  }

  put(endpoint, body, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: "PUT",
      body,
    });
  }

  delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: "DELETE" });
  }
}

const http = new Http();
export default http;
