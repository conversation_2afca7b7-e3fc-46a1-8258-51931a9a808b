import { But<PERSON> } from "@/components/ui/button";
import { ChevronRightCircleIcon } from "lucide-react";
import Link from "next/link";

export default function GalleryBox({ gallery }) {
  return (
    <div className="p-2 flex flex-col shadow-[0_0_11px_2px_var(--theme-color-light)]">
      <div className="group relative flex flex-col">
        <div className="block w-full bg-[var(--theme-color-light)] overflow-hidden">
          <Link href={`/gallery/${gallery.slug}`} className="block">
            <img
              src={gallery.full_thumbnail}
              alt={gallery.title}
              className="object-cover group-hover:scale-105 aspect-[1.7/1] object-center w-full transition-transform duration-300"
            />
          </Link>
        </div>

        <div className="absolute inset-0 bg-gradient-to-t from-brand-primary to-transparent flex flex-col justify-end p-2 pointer-events-none">
          <ul className="flex flex-wrap items-center gap-3">
            {gallery.properties?.slice(0, 3)?.map((property, key) => (
              <li key={key} className="text-xs rounded font-mediu text-white">
                {property}
              </li>
            ))}
          </ul>
        </div>
      </div>
      <div className="p-2 flex flex-col gap-2 flex-1">
        <div>
          <Link href={`/gallery/${gallery.slug}`}>
            <h4 className="text-lg font-black leading-5 line-clamp-2">
              {gallery.title}
            </h4>
          </Link>
        </div>
        <div className="flex justify-between items-center mt-auto">
          <ul className="flex flex-wrap items-center gap-1">
            {gallery.hashtages?.slice(0, 3)?.map((hashtag, key) => (
              <li
                key={key}
                className="text-xs font-semibold text-brand-secondary"
              >
                {hashtag}
              </li>
            ))}
          </ul>
          <Button size="sm" variant="link" asChild className="p-0 py-0">
            <Link href={`/gallery/${gallery.slug}`}>
              Découvrir <ChevronRightCircleIcon />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
