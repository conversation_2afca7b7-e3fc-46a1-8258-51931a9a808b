import DateCountdown from "@/components/date-countdown";
import ProductPrice from "@/components/product-price";
import ProductsSlide from "@/components/products-slide";
import { Badge } from "@/components/ui/badge";
import { getProductStatus, getUrl } from "@/lib/helpers";
import { ProductService } from "@/services/product";
import Link from "next/link";
import ProductActions from "./_components/product-actions";
import ProductCartAction from "./_components/product-cart-action";
import ProductGallery from "./_components/product-gallery";
import ProductTabs from "./_components/product-tabs";

export default async function ProductPage({ params }) {
  const { slug } = params;
  const product = await ProductService.getProduct(slug);

  const productStatus = getProductStatus(product.is_active);

  return (
    <div className="container-lg pb-40">
      <div className="grid grid-cols-12">
        <div className="col-span-full md:col-span-12 lg:col-span-9 xl:col-span-8 my-10">
          <div className="grid grid-cols-2 gap-8">
            <div className="col-span-full md:col-span-1">
              <ProductGallery product={product} />
            </div>
            <div className="col-span-full md:col-span-1">
              {/* Brand */}
              {product.brand && (
                <div className="flex items-center justify-center md:justify-start gap-1 mb-2">
                  <img
                    src={product.brand.full_logo}
                    alt={product.brand.description}
                    className="h-5 w-auto"
                  />
                  <span className="font-bold text-xs">
                    {product.brand.name}
                  </span>
                </div>
              )}

              {/* Title */}
              <div className="mb-3">
                <h1 className="text-2xl [text-align:center] md:!text-left">
                  {product?.title}
                </h1>
              </div>

              {/* Categories */}
              <ul className="flex flex-wrap justify-center md:justify-start gap-1">
                {product.list_categories
                  ?.filter((c) => !c.slug.startsWith("rm-"))
                  ?.map((category) => (
                    <li key={category.id}>
                      <Link href={`/products?categories=${category?.slug}`}>
                        <Badge variant="secondary" size="sm">
                          {category.name}
                        </Badge>
                      </Link>
                    </li>
                  ))}
              </ul>

              {/* Status & Price */}
              <div className="mt-3 pt-3 border-t flex flex-col items-center md:items-start">
                <div className="text-md mb-1">
                  <strong style={{ color: productStatus.color }}>
                    {productStatus.label}
                  </strong>
                </div>

                <ProductPrice product={product} />
              </div>

              {product.active_offer && (
                <div
                  className="glow-effect mt-3 p-2 rounded flex items-center justify-between"
                  style={{ backgroundColor: product.active_offer.offer.color }}
                >
                  <h3 className="text-white font-black text-lg sm:text-xl line-clamp-1">
                    {product.active_offer.offer.title}
                  </h3>
                  <DateCountdown date={product.active_offer.offer.end_at} />
                </div>
              )}

              {/* Actions */}
              <div>
                <ProductCartAction product={product} />
                <ProductActions product={product} />
              </div>
            </div>
          </div>
        </div>
      </div>

      <ProductTabs product={product} />

      <section>
        <ProductsSlide
          title="Produits similaires"
          products={product?.active_related?.map((item) => item.related)}
        />
        <ProductsSlide
          title="Produits accessoires"
          products={product?.active_accessories?.map((item) => item.related)}
        />
      </section>

      {product && <ProductJsonLD product={product} />}
    </div>
  );
}

/** @returns {import("schema-dts").Product} */
function ProductJsonLD({ product }) {
  const jsonLD = {
    "@context": "http://schema.org",
    "@type": "Product",
    name: product.title,
    description: product.plain_description,
    image: product.full_image,
    offers: {
      "@type": "http://schema.org/Offer",
      availability: "http://schema.org/InStock",
      price: product.price_ttc,
      priceSpecification: {
        price: product.price_ttc,
        priceCurrency: "MAD",
        valueAddedTaxIncluded: "true",
      },
      url: getUrl(`/products/${product.slug}`),
      priceCurrency: "MAD",
      priceValidUntil: "2025-12-31",
      seller: {
        "@type": "Organization",
        name: "ECOWATT MAROC",
        url: getUrl(),
      },
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLD) }}
    />
  );
}

/** @return {import("next").Metadata} */
export async function generateMetadata({ params }) {
  const { slug } = params;
  const product = await ProductService.getProduct(slug);

  return {
    title: product.title,
    description: product.plain_description?.slice(0, 300),
    keywords: `${product.title} prix maroc, ${product.title} fiche technique, kit solaire, panneaux solaires, panneau solaire, énergie solaire, Jinko, Canadian Solar, Onduleur Huawei, énergie durable, prix maroc, Maroc, photovoltaïque, électricité renouvelable, économies d'énergie, écologie, installation solaire, fiche technique, optimisation maroc`,
    openGraph: {
      title: product.title,
      description: product.plain_description,
      type: "article",
      url: getUrl(`/products/${product.slug}`),
      images: [product.full_image],
    },
    alternates: {
      canonical: `/products/${product.slug}`,
    },
  };
}
