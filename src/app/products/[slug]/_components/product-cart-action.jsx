"use client";

import { But<PERSON> } from "@/components/ui/button";
import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { cn } from "@/lib/utils";
import { useAuthContext } from "@/providers/auth-provider";
import { MinusIcon, PlusIcon, ShoppingCartIcon } from "lucide-react";
import { useContext, useEffect, useState } from "react";
import { TailSpin } from "react-loader-spinner";

export default function ProductCarTAction({ product }) {
  const { authenticated } = useAuthContext();
  const { cartDataChecker, addToCartMutation, storeGuestCartItem, openPopup } =
    useContext(CartAndWishlistProvider);

  const [currentQuantity, setCurrentQuantity] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const [addLoadingCart, setAddLoadingCart] = useState(false);

  useEffect(() => {
    let cartFilter = cartDataChecker.filter(function (el) {
      return el.id === product?.id;
    });
    if (product && cartFilter.length > 0) {
      cartFilter = cartFilter[0];
      setQuantity(cartFilter?.quantity);
      setCurrentQuantity(cartFilter?.quantity);
    }
  }, [product, cartDataChecker]);

  const updateQuantity = async (qty, force = false) => {
    const newQty = force ? qty : quantity + qty;

    if (newQty >= 0) {
      setQuantity(newQty);
    }
  };

  const handleAddToCard = async () => {
    if (isNaN(quantity) || quantity < 1 || quantity === currentQuantity) return;

    setAddLoadingCart(true);
    try {
      if (authenticated)
        await addToCartMutation({ id: product?.id, quantity: quantity });
      else await storeGuestCartItem({ id: product?.id, quantity: quantity });
      setCurrentQuantity(quantity);

      setAddLoadingCart(false);
      openPopup();
    } catch (error) {
      setAddLoadingCart(false);
    }
  };

  const isActive = [1, 2].includes(product?.is_active);

  return (
    <div className={cn("mt-6", !isActive && "opacity-50 pointer-events-none")}>
      <div className="flex items-stretch gap-2">
        <div className="flex items-center gap-1 bg-secondary/50 p-1 rounded">
          <button
            className="h-full aspect-square bg-white inline-flex justify-center items-center rounded"
            onClick={() => updateQuantity(-1)}
            disabled={addLoadingCart || !isActive}
          >
            <MinusIcon size={16} />
          </button>
          <input
            className="min-w-0 w-20 text-center bg-transparent"
            value={quantity}
            onChange={(e) => {
              const qty = parseInt(e.target.value);
              if (qty) updateQuantity(qty, true);
            }}
          />
          <button
            className="h-full aspect-square bg-white inline-flex justify-center items-center rounded"
            onClick={() => updateQuantity(1)}
            disabled={addLoadingCart || !isActive}
          >
            <PlusIcon size={16} />
          </button>
        </div>
        <Button
          variant="brand-ternary"
          size="lg"
          className="flex-1"
          onClick={handleAddToCard}
          disabled={addLoadingCart || !isActive}
        >
          {addLoadingCart ? (
            <TailSpin
              color="#fff"
              height={20}
              width={20}
              visible={addLoadingCart}
            />
          ) : (
            <ShoppingCartIcon />
          )}
          Ajouter au panier
        </Button>
      </div>
    </div>
  );
}
