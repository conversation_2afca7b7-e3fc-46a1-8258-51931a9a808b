"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import HtmlContent from "@/shared/components/html-content";
import { FileSpreadsheetIcon } from "lucide-react";
import "moment/locale/fr";
import Link from "next/link";
import { useQueryState } from "nuqs";
import ReviewsTab from "./reviews-tab";

export default function ProductTabs({ product }) {
  const [tab] = useQueryState("tab", { defaultValue: "description" });

  return (
    <div>
      <Tabs defaultValue="description" value={tab}>
        <TabsList className="w-full">
          <TabsTrigger value="description" asChild>
            <Link scroll={false} shallow={true} href="?tab=description">
              Description
            </Link>
          </TabsTrigger>
          {product?.product_properties?.length > 0 && (
            <TabsTrigger value="properties" asChild>
              <Link scroll={false} shallow={true} href="?tab=properties">
                Caractéristiques
              </Link>
            </TabsTrigger>
          )}
          {product?.full_technical_file && (
            <TabsTrigger value="technical-file" asChild>
              <Link scroll={false} shallow={true} href="?tab=technical-file">
                Fiche technique
              </Link>
            </TabsTrigger>
          )}
          <TabsTrigger value="reviews" asChild>
            <Link scroll={false} shallow={true} href="?tab=reviews">
              Avis client
            </Link>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="description">
          <HtmlContent html={product?.description} />
        </TabsContent>
        <TabsContent value="properties">
          <div className="grid grid-cols-2 sm:grid-cols-3">
            {product?.product_properties.map((item) => (
              <div key={item.id} className="cols-span-full md:col-span-1">
                <strong>{item?.property?.label}</strong> : {item?.value}{" "}
                {item?.measure?.label}
              </div>
            ))}
          </div>
        </TabsContent>
        <TabsContent value="technical-file">
          <Button size="lg" asChild>
            <a
              href={product?.full_technical_file}
              rel="noreferrer"
              target="_blank"
            >
              <FileSpreadsheetIcon /> Consulter la fiche technique
            </a>
          </Button>
        </TabsContent>
        <TabsContent value="reviews">
          <ReviewsTab product={product} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
