"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Dialog<PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useAuthContext } from "@/providers/auth-provider";
import { useSubmitProductReviewMutation } from "@/services/customer";
import { useProductReviews } from "@/services/product";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { ChevronDownIcon, XIcon } from "lucide-react";
import "moment/locale/fr";
import { useEffect, useRef, useState } from "react";
import ReviewCard from "./review-card";

export default function ReviewsTab({ product }) {
  const { reviews } = useProductReviews(product.id, {
    sort: "highest-rating",
  });

  return (
    <div>
      <div className="grid md:grid-cols-2 gap-10 mb-6">
        <ReviewStats product={product} />
        <SubmitReviewForm product={product} />
      </div>

      {reviews?.data?.length > 0 && (
        <div className="mt-6">
          <div>
            <h2 className="font-semibold mb-3 text-lg">
              Ce qu'ils disent sur ce produit
            </h2>
          </div>
          <div className="grid md:grid-cols-2 gap-10">
            {reviews?.data?.map((review) => (
              <ReviewCard key={review.id} review={review} />
            ))}
          </div>
        </div>
      )}

      {reviews?.last_page > 1 && (
        <div className="flex justify-center mt-4">
          <AllReviews product={product} />
        </div>
      )}
    </div>
  );
}

function ReviewStats({ product }) {
  return (
    <div>
      <h2 className="font-semibold mb-3 text-lg">
        Laissez votre avis sur ce produit
      </h2>
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="font-black text-md">
            {product.reviews_count} avis
          </span>
          <span className="text-border">|</span>
          <div className="flex items-center text-lg">
            {Array.from({ length: 5 }, (_, i) => {
              const starValue = i + 1;
              let fillClass = "";

              if (starValue <= Math.floor(product.rating)) {
                // Full star
                fillClass = "text-[#FFAE00]";
              } else if (
                starValue === Math.ceil(product.rating) &&
                product.rating % 1 !== 0
              ) {
                // Partial star
                const percentage =
                  (product.rating - Math.floor(product.rating)) * 100;
                return (
                  <span key={i} className="relative text-gray-200">
                    <span
                      className="absolute top-0 left-0 text-[#FFAE00] overflow-hidden"
                      style={{ width: `${percentage}%` }}
                    >
                      ★
                    </span>
                    ★
                  </span>
                );
              } else {
                // Empty star
                fillClass = "text-gray-200";
              }

              return (
                <span key={i} className={`relative ${fillClass}`}>
                  ★
                </span>
              );
            })}
          </div>
          <span className="font-medium">{product.rating} sur 5</span>
        </div>
        <ul className="flex flex-col-reverse gap-2">
          {Array.from({ length: 5 }, (_, key) => (
            <li key={key} className="flex items-center gap-2">
              <span className="text-lg font-medium">{key + 1}</span>
              <span className="text-lg text-[#FFAE00]">★</span>
              <div className="flex-1 h-6 bg-gray-300 rounded-sm overflow-hidden">
                <div
                  className="h-full bg-[#FFAE00] transition-[width]"
                  style={{
                    width: `${product.rating_stats?.[key + 1]?.["percentage"] ?? 0}%`,
                  }}
                ></div>
              </div>
              <div className="min-w-[60px]">
                <span className="text-md font-medium">
                  {product.rating_stats?.[key + 1]?.["count"] ?? 0}
                </span>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

function SubmitReviewForm({ product }) {
  const [rating, setRating] = useState("5");
  const [comment, setComment] = useState("");
  const { requireAuthAction } = useAuthContext();

  const { submitProductReview, isLoading, isSuccess, error } =
    useSubmitProductReviewMutation();

  function handleSubmit(e) {
    e.preventDefault();

    requireAuthAction(() => {
      submitProductReview(
        {
          productId: product.id,
          data: {
            rating,
            comment,
          },
        },
        {
          onSuccess() {
            setComment("");
            setRating("5");
          },
        }
      );
    });
  }

  return (
    <div className="flex flex-col">
      <h2 className="font-semibold mb-3 text-lg invisible">
        Laissez votre avis sur ce produit
      </h2>
      <div className="space-y-4 flex-1">
        <form
          className="flex flex-col md:max-w-lg gap-2 h-full"
          onSubmit={handleSubmit}
        >
          <div className="space-y-1">
            <label htmlFor="comment">Partagez votre expérience</label>
            <Textarea
              id="comment"
              placeholder="Partagez votre expérience…"
              className="bg-secondary flex-1"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              maxlength="500"
            />
          </div>
          <div className="space-y-1">
            <label htmlFor="rating">
              Notez votre expérience (1 à 5 étoiles)
            </label>
            <Select id="rating" value={rating} onValueChange={setRating}>
              <SelectTrigger className="bg-secondary border-none">
                <SelectValue placeholder="Filtrer par les étoiles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">
                  <span className="text-[#FFAE00]">★</span>
                  <span className="text-gray-200">★★★★</span>
                </SelectItem>
                <SelectItem value="2">
                  <span className="text-[#FFAE00]">★★</span>
                  <span className="text-gray-200">★★★</span>
                </SelectItem>
                <SelectItem value="3">
                  <span className="text-[#FFAE00]">★★★</span>
                  <span className="text-gray-200">★★</span>
                </SelectItem>
                <SelectItem value="4">
                  <span className="text-[#FFAE00]">★★★★</span>
                  <span className="text-gray-200">★</span>
                </SelectItem>
                <SelectItem value="5">
                  <span className="text-[#FFAE00]">★★★★★</span>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="brand-ternary" disabled={isLoading}>
            Publier mon avis
          </Button>
          {isSuccess && (
            <SuccessSnackbar message="Merci pour votre avis, il sera visible après modération." />
          )}
          {error && (
            <ErrorSnackbar message="On ne peut enregistrer votre avis pour le moment, essayez plus tard" />
          )}
        </form>
      </div>
    </div>
  );
}

function AllReviews({ product }) {
  const dialogRef = useRef();
  const [page, setPage] = useState(1);
  const [sort, setSort] = useState("");
  const [stars, setStars] = useState("");
  const { reviews } = useProductReviews(product.id, { page, sort, stars });

  useEffect(() => {
    dialogRef.current?.scrollTo({ top: 0 });
  }, [page]);

  useEffect(() => {
    setPage(1);
  }, [sort, stars]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="lg" variant="outline" className="rounded-full">
          Voir tous les avis <ChevronDownIcon />
        </Button>
      </DialogTrigger>
      <DialogContent
        ref={dialogRef}
        className="sm:max-w-4xl max-h-[85vh] overflow-y-scroll"
      >
        <DialogHeader>
          <DialogTitle>Avis sur l'article</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <ul className="flex items-center overflow-x-auto gap-2">
              <li>
                <Select value={sort} onValueChange={setSort}>
                  <SelectTrigger className="sm:min-w-[200px]">
                    <SelectValue placeholder="Filtrer par évaluation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="highest-rating">
                      Plus hautes évaluations
                    </SelectItem>
                    <SelectItem value="lowest-rating">
                      Plus basses évaluations
                    </SelectItem>
                  </SelectContent>
                </Select>
              </li>
              <li>
                <Select value={stars} onValueChange={setStars}>
                  <SelectTrigger className="sm:min-w-[200px]">
                    <SelectValue placeholder="Filtrer par les étoiles" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">
                      <span className="text-[#FFAE00]">★</span>
                      <span className="text-gray-200">★★★★</span>
                    </SelectItem>
                    <SelectItem value="2">
                      <span className="text-[#FFAE00]">★★</span>
                      <span className="text-gray-200">★★★</span>
                    </SelectItem>
                    <SelectItem value="3">
                      <span className="text-[#FFAE00]">★★★</span>
                      <span className="text-gray-200">★★</span>
                    </SelectItem>
                    <SelectItem value="4">
                      <span className="text-[#FFAE00]">★★★★</span>
                      <span className="text-gray-200">★</span>
                    </SelectItem>
                    <SelectItem value="5">
                      <span className="text-[#FFAE00]">★★★★★</span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </li>
            </ul>
          </div>
          <div>
            {reviews?.data?.map((review) => (
              <ReviewCard key={review.id} review={review} />
            ))}
          </div>
          {reviews?.data?.length === 0 && (
            <div className="flex flex-col items-center py-10 gap-2">
              <h6 className="text-xl font-semibold text-brand-primary">
                Aucune avi trouvé!
              </h6>
              {(sort || stars) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSort("");
                    setStars("");
                  }}
                >
                  <XIcon /> Effacer les filtres
                </Button>
              )}
            </div>
          )}
          {reviews?.last_page > 1 && (
            <div>
              <Pagination className="overflow-x-scroll">
                <PaginationContent>
                  {page > 1 && (
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={() => setPage((p) => p - 1)}
                      />
                    </PaginationItem>
                  )}
                  {Array.from({ length: reviews?.last_page }, (_, key) => (
                    <PaginationItem key={key}>
                      <PaginationLink
                        href="#"
                        isActive={key + 1 === page}
                        onClick={() => setPage(key + 1)}
                      >
                        {key + 1}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  {page < reviews?.last_page && (
                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={() => setPage((p) => p + 1)}
                      />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
