import Breadcrumb from "@/shared/components/breadcrumb";
import ProductsContainer from "./_components/products-container";

export default function Products() {
  return (
    <>
      <Breadcrumb title="Nos produits" />
      <ProductsContainer />
    </>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Les produits solaires, électriques, et hydrauliques",
  description:
    "Achetez la première plateforme au Maroc pour l'énergie durable: solutions solaires photovoltaïques, électricité industrielle et hydraulique.",
  keywords:
    "énergie solaire, énergie durable, photovoltaïque, électricité industrielle, hydraulique, Maroc, solutions solaires, écologie, renouvelable, panneaux solaires, Agadir, installation solaire, kit solaire, kit, onduleur, jinko",
  alternates: {
    canonical: "/products",
  },
};
