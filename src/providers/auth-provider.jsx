"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CircleUserRoundIcon } from "lucide-react";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { createContext, useContext, useState } from "react";

const AuthContext = createContext({
  currentUser: null,
  authenticated: false,
  requireAuthAction: () => {},
});

export const useAuthContext = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuthContext must be used within AuthContext");
  }

  return context;
};

export function AuthProvider({ children, currentUser }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [requireAuthAlertOpen, setRequireAuth<PERSON>lertOpen] = useState(null);

  function requireAuthAction(fn) {
    if (!currentUser) {
      setRequireAuthAlertOpen(true);
    } else {
      fn();
    }
  }

  const redirect = `${pathname}?${searchParams.toString()}`;

  return (
    <AuthContext.Provider
      value={{
        currentUser,
        authenticated: Boolean(currentUser && currentUser.id),
        requireAuthAction,
      }}
    >
      {children}
      <Dialog
        open={requireAuthAlertOpen}
        onOpenChange={setRequireAuthAlertOpen}
      >
        <DialogContent>
          <div className="flex justify-center">
            <CircleUserRoundIcon size={45} className="text-brand-secondary" />
          </div>
          <DialogHeader>
            <DialogTitle className="text-center">
              Veuillez vous connecter pour continuer.
            </DialogTitle>
            <DialogDescription className="text-center">
              Connectez-vous ou créez votre compte en toute simplicité!
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex items-center gap-2">
            <Button
              type="submit"
              className="w-full"
              asChild
              onClick={() => setRequireAuthAlertOpen(false)}
            >
              <Link href={`/login?redirect=${redirect}`}>Se connecter</Link>
            </Button>
            <Button
              type="submit"
              variant="outline"
              className="w-full"
              asChild
              onClick={() => setRequireAuthAlertOpen(false)}
            >
              <Link href={`/register?redirect=${redirect}`}>S'inscrire</Link>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AuthContext.Provider>
  );
}
